app = "atlas-agents-staging-rabbitmq"
primary_region = "iad"
kill_signal = "SIGTERM"
kill_timeout = "30s"

[build]
  dockerfile = "Dockerfile.rabbitmq"

[env]
  RABBITMQ_DEFAULT_USER = "atlas"
  RABBITMQ_DEFAULT_PASS = "atlas-rabbitmq-secret"
  RABBITMQ_DEFAULT_VHOST = "/"

[[services]]
  internal_port = 5672
  protocol = "tcp"
  
  [[services.ports]]
    port = 5672

[[services]]
  internal_port = 15672
  protocol = "tcp"
  
  [[services.ports]]
    port = 15672
    handlers = ["http"]

[[vm]]
  size = "shared-cpu-1x"
  memory = "1gb"

[mounts]
  source = "rabbitmq_data"
  destination = "/var/lib/rabbitmq"