app = "atlas-agents-staging"
primary_region = "iad"
kill_signal = "SIGTERM"
kill_timeout = "300s"

[build]
  dockerfile = "Dockerfile"

[env]
  ENV_MODE = "production"
  PYTHONPATH = "/app"
  LOG_LEVEL = "INFO"
  PORT = "8000"
  # Fly.io typically provides 1-2 shared CPUs per machine
  # Adjusting workers for Fly's smaller machines
  WORKERS = "4"
  THREADS = "2"
  WORKER_CONNECTIONS = "1000"
  # Internal Redis connection
  REDIS_HOST = "atlas-agents-staging-redis.internal"
  REDIS_PORT = "6379"
  REDIS_PASSWORD = ""
  REDIS_SSL = "false"
  # Internal RabbitMQ connection
  RABBITMQ_HOST = "atlas-agents-staging-rabbitmq.internal"
  RABBITMQ_PORT = "5672"
  RABBITMQ_USER = "atlas"
  RABBITMQ_PASSWORD = "atlas-rabbitmq-secret"
  RABBITMQ_VHOST = "/"

[processes]
  api = "sh -c 'uv run gunicorn api:app --workers $WORKERS --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000 --timeout 1800 --graceful-timeout 600 --keep-alive 1800 --max-requests 0 --max-requests-jitter 0 --forwarded-allow-ips \"*\" --worker-connections $WORKER_CONNECTIONS --worker-tmp-dir /dev/shm --preload --log-level info --access-logfile - --error-logfile - --capture-output --enable-stdio-inheritance --threads $THREADS'"
  worker = "uv run dramatiq --skip-logging --processes 2 --threads 2 run_agent_background"

[[services]]
  internal_port = 8000
  processes = ["api"]
  protocol = "tcp"
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1

  [services.concurrency]
    type = "requests"
    hard_limit = 250
    soft_limit = 200

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]

  [[services.ports]]
    port = 80
    handlers = ["http"]

  [[services.http_checks]]
    interval = "30s"
    timeout = "10s"
    grace_period = "40s"
    method = "GET"
    path = "/api/health"

[mounts]
  source = "atlas_logs"
  destination = "/app/logs"
  processes = ["api", "worker"]

[[vm]]
  size = "shared-cpu-2x"
  memory = "4gb"
  processes = ["api"]

[[vm]]
  size = "shared-cpu-1x"
  memory = "2gb"
  processes = ["worker"]
