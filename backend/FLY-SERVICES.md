# Fly.io Services Setup

Your Atlas backend is now running with internal Fly.io services:

## Services Overview

### 1. **Backend API & Worker**
- App: `atlas-agents-staging`
- URL: https://atlas-agents-staging.fly.dev
- API: 2x shared CPU, 4GB RAM
- Worker: 1x shared CPU, 2GB RAM

### 2. **Redis**
- App: `atlas-agents-staging-redis`
- Internal URL: `atlas-agents-staging-redis.internal:6379`
- No password required (internal network)
- 1GB persistent storage

### 3. **RabbitMQ**
- App: `atlas-agents-staging-rabbitmq`
- Internal URL: `atlas-agents-staging-rabbitmq.internal:5672`
- Username: `atlas`
- Password: `atlas-rabbitmq-secret`
- Management UI: http://atlas-agents-staging-rabbitmq.fly.dev:15672/
- 2GB persistent storage

## Deployment Commands

### Deploy Backend
```bash
cd backend
fly deploy --app atlas-agents-staging
```

### Monitor Services
```bash
# Check status
fly status --app atlas-agents-staging
fly status --app atlas-agents-staging-redis
fly status --app atlas-agents-staging-rabbitmq

# View logs
fly logs --app atlas-agents-staging
fly logs --app atlas-agents-staging-redis
fly logs --app atlas-agents-staging-rabbitmq

# Scale machines
fly scale count api=2 worker=1 --app atlas-agents-staging
```

### Access Services
```bash
# SSH into machines
fly ssh console --app atlas-agents-staging
fly ssh console --app atlas-agents-staging-redis
fly ssh console --app atlas-agents-staging-rabbitmq

# Access RabbitMQ Management UI
open http://atlas-agents-staging-rabbitmq.fly.dev:15672/
# Login with: atlas / atlas-rabbitmq-secret
```

## Benefits of This Setup
- ✅ No external service dependencies
- ✅ Lower latency (same region, internal network)
- ✅ No SSL/TLS complexity
- ✅ Cost effective (just Fly machine costs)
- ✅ Better security (internal network only)
- ✅ Persistent storage with automatic backups

## Troubleshooting
- If services can't connect, ensure all apps are in the same region (iad)
- Internal DNS uses format: `<app-name>.internal`
- No SSL required for internal connections
- Services automatically restart on failure