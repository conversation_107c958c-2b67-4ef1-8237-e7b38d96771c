#!/bin/bash
# Template for setting Fly.io secrets for Atlas backend
# Copy this file and fill in your actual values

# Core API Configuration
fly secrets set \
  SUPABASE_URL="your-supabase-url" \
  SUPABASE_ANON_KEY="your-supabase-anon-key" \
  SUPABASE_SERVICE_KEY="your-supabase-service-key" \
  JWT_SECRET="your-jwt-secret" \
  --app Atlas-agents-staging

# Redis Configuration (Upstash)
fly secrets set \
  REDIS_HOST="your-upstash-redis-endpoint" \
  REDIS_PORT="your-redis-port" \
  REDIS_PASSWORD="your-redis-password" \
  --app Atlas-agents-staging

# RabbitMQ Configuration (CloudAMQP)
fly secrets set \
  RABBITMQ_HOST="your-cloudamqp-host" \
  RABBITMQ_PORT="5672" \
  RABBITMQ_USER="your-rabbitmq-user" \
  RABBITMQ_PASSWORD="your-rabbitmq-password" \
  --app Atlas-agents-staging

# LLM API Keys
fly secrets set \
  OPENAI_API_KEY="your-openai-api-key" \
  ANTHROPIC_API_KEY="your-anthropic-api-key" \
  --app Atlas-agents-staging

# Optional Services
fly secrets set \
  STRIPE_SECRET_KEY="your-stripe-secret-key" \
  LANGFUSE_SECRET_KEY="your-langfuse-key" \
  LANGFUSE_PUBLIC_KEY="your-langfuse-public-key" \
  LANGFUSE_HOST="your-langfuse-host" \
  --app Atlas-agents-staging

# AWS S3 (if using)
fly secrets set \
  AWS_ACCESS_KEY_ID="your-aws-access-key" \
  AWS_SECRET_ACCESS_KEY="your-aws-secret-key" \
  AWS_REGION="your-aws-region" \
  S3_BUCKET_NAME="your-s3-bucket" \
  --app Atlas-agents-staging

# Email Service
fly secrets set \
  EMAIL_HOST="your-smtp-host" \
  EMAIL_PORT="587" \
  EMAIL_USERNAME="your-email-username" \
  EMAIL_PASSWORD="your-email-password" \
  EMAIL_FROM="<EMAIL>" \
  --app Atlas-agents-staging
