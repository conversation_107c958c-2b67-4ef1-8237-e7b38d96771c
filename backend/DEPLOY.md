# Deploy Atlas Backend to Fly.io

## Prerequisites

1. **Set up external services:**
   - **Redis**: Create an Upstash Redis instance at https://console.upstash.com/
   - **RabbitMQ**: Create a CloudAMQP instance at https://www.cloudamqp.com/

2. **Get your credentials ready** from your `.env` file

## Deployment Steps

### 1. Set Production Secrets

```bash
cd backend

# Set all required secrets
fly secrets set \
  ENV_MODE="production" \
  SUPABASE_URL="<your-supabase-url>" \
  SUPABASE_ANON_KEY="<your-supabase-anon-key>" \
  SUPABASE_SERVICE_ROLE_KEY="<your-supabase-service-key>" \
  REDIS_HOST="<upstash-redis-endpoint>" \
  REDIS_PORT="<redis-port>" \
  REDIS_PASSWORD="<redis-password>" \
  REDIS_SSL="true" \
  RABBITMQ_HOST="<cloudamqp-host>" \
  RABBITMQ_PORT="5672" \
  RABBITMQ_USER="<rabbitmq-user>" \
  RABBITMQ_PASSWORD="<rabbitmq-password>" \
  ANTHROPIC_API_KEY="<your-anthropic-key>" \
  OPENAI_API_KEY="<your-openai-key>" \
  MODEL_TO_USE="<your-default-model>" \
  MCP_CREDENTIAL_ENCRYPTION_KEY="<32-char-encryption-key>" \
  --app atlas-agents-staging
```

### 2. Create Persistent Volume (first time only)

```bash
fly volumes create atlas_logs --size 10 --region iad --app atlas-agents-staging
```

### 3. Deploy

```bash
fly deploy --app atlas-agents-staging
```

### 4. Scale Machines

```bash
# Scale API and worker processes
fly scale count api=2 worker=1 --app atlas-agents-staging
```

### 5. Monitor

```bash
# Check status
fly status --app atlas-agents-staging

# View logs
fly logs --app atlas-agents-staging

# SSH into machine (for debugging)
fly ssh console --app atlas-agents-staging
```

## Additional Secrets (Optional)

Use the `fly-secrets-template.sh` as reference for additional services:
- AWS S3 credentials
- Email service configuration
- Stripe, Langfuse, etc.

## Notes

- The `fly.toml` is configured for production with proper worker counts and memory limits
- Health checks are enabled at `/api/health`
- Logs are persisted to the `atlas_logs` volume
- Auto-scaling is configured for the API service